package com.example.coloringproject.utils

import com.example.coloringproject.data.Region
import kotlin.math.*

/**
 * 动态大小计算器
 * 根据当前项目的区域大小分布动态调整数字大小基准
 */
class DynamicSizeCalculator {
    
    companion object {
        private const val BASE_TEXT_SIZE = 16f
        private const val MIN_TEXT_SIZE = 10f
        private const val MAX_TEXT_SIZE = 32f
        
        // 缓存计算结果，避免重复计算
        private var cachedRegionsHash = 0
        private var cachedDynamicBaseline = 50f
        private var cachedSizeRange = Pair(20f, 200f)
    }
    
    /**
     * 根据项目动态计算数字大小
     */
    fun calculateDynamicTextSize(
        regionSize: Float,
        regions: List<Region>,
        scale: Float
    ): Float {
        val currentHash = regions.hashCode()
        
        // 如果区域数据没变，使用缓存的基准
        if (currentHash != cachedRegionsHash) {
            updateDynamicBaseline(regions)
            cachedRegionsHash = currentHash
        }
        
        val scaledSize = regionSize * scale
        val ratio = scaledSize / cachedDynamicBaseline
        
        // 使用动态范围进行缩放
        val normalizedRatio = normalizeRatio(ratio, cachedSizeRange)
        val textSize = BASE_TEXT_SIZE * (0.6f + normalizedRatio * 1.4f)  // 0.6x到2.0x
        
        return textSize.coerceIn(MIN_TEXT_SIZE, MAX_TEXT_SIZE)
    }
    
    /**
     * 更新动态基准
     */
    private fun updateDynamicBaseline(regions: List<Region>) {
        if (regions.isEmpty()) {
            cachedDynamicBaseline = 50f
            cachedSizeRange = Pair(20f, 200f)
            return
        }
        
        // 计算所有区域的原始大小
        val regionSizes = regions.map { getRegionSize(it) }
        
        // 使用中位数作为基准，比平均数更稳定
        val sortedSizes = regionSizes.sorted()
        cachedDynamicBaseline = when {
            sortedSizes.isEmpty() -> 50f
            sortedSizes.size % 2 == 0 -> {
                val mid = sortedSizes.size / 2
                (sortedSizes[mid - 1] + sortedSizes[mid]) / 2f
            }
            else -> sortedSizes[sortedSizes.size / 2]
        }
        
        // 计算有效范围（去除极值）
        val p25 = sortedSizes[(sortedSizes.size * 0.25).toInt()]
        val p75 = sortedSizes[(sortedSizes.size * 0.75).toInt()]
        cachedSizeRange = Pair(p25, p75)
        
        android.util.Log.d("DynamicSize", 
            "项目动态基准更新: 基准=${cachedDynamicBaseline.toInt()}px, " +
            "范围=${p25.toInt()}-${p75.toInt()}px, 区域数=${regions.size}"
        )
    }
    
    /**
     * 标准化比例，考虑项目的大小分布
     */
    private fun normalizeRatio(ratio: Float, sizeRange: Pair<Float, Float>): Float {
        val (minSize, maxSize) = sizeRange
        val baseline = cachedDynamicBaseline
        
        return when {
            ratio <= minSize / baseline -> 0f  // 最小区域
            ratio >= maxSize / baseline -> 1f  // 最大区域
            else -> {
                // 在有效范围内线性插值
                val normalizedRatio = (ratio - minSize / baseline) / (maxSize / baseline - minSize / baseline)
                normalizedRatio.coerceIn(0f, 1f)
            }
        }
    }
    
    /**
     * 计算区域原始大小
     */
    private fun getRegionSize(region: Region): Float {
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                val width = bbox[2] - bbox[0]
                val height = bbox[3] - bbox[1]
                return min(width, height).toFloat()
            }
        }
        return sqrt(region.pixels.size.toFloat())
    }
    
    /**
     * 获取当前项目的统计信息
     */
    fun getProjectStats(): String {
        return "动态基准: ${cachedDynamicBaseline.toInt()}px, " +
               "有效范围: ${cachedSizeRange.first.toInt()}-${cachedSizeRange.second.toInt()}px"
    }
    
    /**
     * 重置缓存（用于测试）
     */
    fun resetCache() {
        cachedRegionsHash = 0
        cachedDynamicBaseline = 50f
        cachedSizeRange = Pair(20f, 200f)
    }
}