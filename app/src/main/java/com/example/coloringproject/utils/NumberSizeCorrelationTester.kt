package com.example.coloringproject.utils

import android.util.Log
import com.example.coloringproject.data.Region

/**
 * 数字大小关联性测试器
 * 验证数字大小与区域大小的正相关关系
 */
object NumberSizeCorrelationTester {
    
    private const val TAG = "NumberSizeTest"
    
    /**
     * 测试数字大小与区域大小的关联性
     */
    fun testSizeCorrelation(regions: List<Region>, scaleFactor: Float) {
        Log.d(TAG, "=== 数字大小关联性测试 ===")
        Log.d(TAG, "缩放因子: $scaleFactor")
        
        // 找到同一颜色的不同大小区域进行测试
        val colorGroups = regions.groupBy { normalizeColor(it.colorHex) }
        
        colorGroups.forEach { (color, regionList) ->
            if (regionList.size >= 2) {
                testColorGroup(color, regionList, scaleFactor)
            }
        }
    }
    
    /**
     * 测试同一颜色的不同区域
     */
    private fun testColorGroup(color: String, regions: List<Region>, scaleFactor: Float) {
        val regionSizes = regions.map { region ->
            val size = getRegionSize(region) * scaleFactor
            val textSize = calculateTextSize(size)
            Triple(region.id, size, textSize)
        }.sortedBy { it.second }  // 按区域大小排序
        
        if (regionSizes.size >= 3) {
            val smallest = regionSizes.first()
            val largest = regionSizes.last()
            val middle = regionSizes[regionSizes.size / 2]
            
            Log.d(TAG, "颜色 $color 的区域大小对比:")
            Log.d(TAG, "  最小区域: ${smallest.second.toInt()}px -> 数字${smallest.third.toInt()}px")
            Log.d(TAG, "  中等区域: ${middle.second.toInt()}px -> 数字${middle.third.toInt()}px")
            Log.d(TAG, "  最大区域: ${largest.second.toInt()}px -> 数字${largest.third.toInt()}px")
            
            // 检查是否正相关
            val isPositiveCorrelation = smallest.third < middle.third && middle.third < largest.third
            val sizeRatio = largest.second / smallest.second
            val textRatio = largest.third / smallest.third
            
            Log.d(TAG, "  区域大小比例: ${String.format("%.2f", sizeRatio)}:1")
            Log.d(TAG, "  数字大小比例: ${String.format("%.2f", textRatio)}:1")
            
            if (isPositiveCorrelation && textRatio > 1.3f) {
                Log.d(TAG, "  ✅ 正相关关系良好")
            } else {
                Log.w(TAG, "  ⚠️ 正相关关系需要改进")
            }
        }
    }
    
    /**
     * 计算区域大小
     */
    private fun getRegionSize(region: Region): Float {
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                val width = bbox[2] - bbox[0]
                val height = bbox[3] - bbox[1]
                return kotlin.math.min(width, height).toFloat()
            }
        }
        return kotlin.math.sqrt(region.pixels.size.toFloat())
    }
    
    /**
     * 计算文字大小
     */
    private fun calculateTextSize(displaySize: Float): Float {
        val ratio = displaySize / 50f
        val baseSize = 16f
        val scaledSize = baseSize * (0.6f + ratio * 0.8f)
        return scaledSize.coerceIn(10f, 32f)
    }
    
    /**
     * 颜色标准化
     */
    private fun normalizeColor(colorHex: String): String {
        var color = colorHex.trim().lowercase()
        if (!color.startsWith("#")) color = "#$color"
        if (color.length == 4) {
            val r = color[1]
            val g = color[2]
            val b = color[3]
            color = "#$r$r$g$g$b$b"
        }
        return color
    }
    
    /**
     * 生成测试报告
     */
    fun generateTestReport(regions: List<Region>, scaleFactor: Float): String {
        val colorGroups = regions.groupBy { normalizeColor(it.colorHex) }
        val testableColors = colorGroups.filter { it.value.size >= 2 }
        
        var positiveCorrelations = 0
        var totalTests = 0
        
        testableColors.forEach { (_, regionList) ->
            val regionSizes = regionList.map { region ->
                val size = getRegionSize(region) * scaleFactor
                val textSize = calculateTextSize(size)
                Pair(size, textSize)
            }.sortedBy { it.first }
            
            if (regionSizes.size >= 2) {
                totalTests++
                val smallest = regionSizes.first()
                val largest = regionSizes.last()
                val textRatio = largest.second / smallest.second
                
                if (textRatio > 1.2f) {
                    positiveCorrelations++
                }
            }
        }
        
        val correlationRate = if (totalTests > 0) positiveCorrelations * 100 / totalTests else 0
        
        return "关联性测试结果: $positiveCorrelations/$totalTests 通过 (${correlationRate}%)"
    }
}