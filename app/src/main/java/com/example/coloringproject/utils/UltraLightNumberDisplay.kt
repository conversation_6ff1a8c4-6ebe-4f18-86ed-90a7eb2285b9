package com.example.coloringproject.utils

import android.graphics.*
import com.example.coloringproject.data.Region
import kotlin.math.*

/**
 * 超轻量级数字显示管理器
 * 专为解决卡顿问题设计，性能优先
 */
class UltraLightNumberDisplay {

    companion object {
        private const val MIN_SCALE = 1.5f
        private const val MAX_NUMBERS = 15  // 大幅减少显示数量
        private const val MIN_SIZE = 40f    // 只显示较大区域
        private const val BASE_TEXT_SIZE = 16f
    }

    // 极简Paint对象
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.BLACK
        textAlign = Paint.Align.CENTER
        typeface = Typeface.DEFAULT_BOLD
        textSize = BASE_TEXT_SIZE
    }

    private val strokePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.WHITE
        textAlign = Paint.Align.CENTER
        typeface = Typeface.DEFAULT_BOLD
        style = Paint.Style.STROKE
        strokeWidth = 2f
        textSize = BASE_TEXT_SIZE
    }

    /**
     * 超简化的数字显示计算
     */
    fun drawNumbers(
        canvas: Canvas,
        regions: List<Region>,
        filledRegions: Set<Int>,
        scale: Float,
        visibleRect: RectF
    ) {
        // 缩放太小时不显示
        if (scale < MIN_SCALE) return

        var count = 0
        val colorMap = createSimpleColorMap(regions)
        val sizeInfoList = mutableListOf<Triple<Int, Float, Float>>()  // 数字, 区域大小, 文字大小

        // 直接遍历，找到最大的几个区域
        for (region in regions) {
            if (count >= MAX_NUMBERS) break
            if (filledRegions.contains(region.id)) continue

            val size = getRegionSize(region) * scale
            if (size < MIN_SIZE) continue

            val center = getRegionCenter(region)
            if (!visibleRect.contains(center.x, center.y)) continue

            val colorNumber = colorMap[normalizeColor(region.colorHex)] ?: 1
            val textSize = calculateTextSize(size)

            // 记录大小信息用于调试
            sizeInfoList.add(Triple(colorNumber, size, textSize))

            // 直接绘制，不做复杂计算
            drawSingleNumber(canvas, center.x, center.y, colorNumber, textSize)
            count++
        }

        // 每隔一段时间输出大小关联信息
        if (count > 0 && System.currentTimeMillis() % 100 < 20) {
            logSizeCorrelation(sizeInfoList)
        }
    }

    /**
     * 输出大小关联信息
     */
    private fun logSizeCorrelation(sizeInfoList: List<Triple<Int, Float, Float>>) {
        if (sizeInfoList.size < 2) return

        val sorted = sizeInfoList.sortedBy { it.second }  // 按区域大小排序
        val smallest = sorted.first()
        val largest = sorted.last()

        android.util.Log.d("NumberSizeCorr", 
            "数字大小关联: 最小区域${smallest.second.toInt()}px->数字${smallest.third.toInt()}px, " +
            "最大区域${largest.second.toInt()}px->数字${largest.third.toInt()}px, " +
            "比例${String.format("%.2f", largest.third / smallest.third)}:1"
        )
    }

    /**
     * 绘制单个数字
     */
    private fun drawSingleNumber(canvas: Canvas, x: Float, y: Float, number: Int, textSize: Float) {
        strokePaint.textSize = textSize
        textPaint.textSize = textSize

        val text = number.toString()
        val textY = y + textSize * 0.3f

        // 先白边后黑字
        canvas.drawText(text, x, textY, strokePaint)
        canvas.drawText(text, x, textY, textPaint)
    }

    /**
     * 极简区域大小计算
     */
    private fun getRegionSize(region: Region): Float {
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                val width = bbox[2] - bbox[0]
                val height = bbox[3] - bbox[1]
                return min(width, height).toFloat()
            }
        }
        return sqrt(region.pixels.size.toFloat())
    }

    /**
     * 极简区域中心计算
     */
    private fun getRegionCenter(region: Region): PointF {
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                return PointF(
                    (bbox[0] + bbox[2]) / 2f,
                    (bbox[1] + bbox[3]) / 2f
                )
            }
        }

        if (region.pixels.isEmpty()) return PointF(0f, 0f)

        var sumX = 0f
        var sumY = 0f
        region.pixels.forEach { pixel ->
            sumX += pixel[0]
            sumY += pixel[1]
        }
        return PointF(sumX / region.pixels.size, sumY / region.pixels.size)
    }

    /**
     * 改进的文字大小计算 - 与区域大小强正相关（修复版本）
     */
    private fun calculateTextSize(regionSize: Float): Float {
        // 根据实际区域大小调整基准值和范围
        val ratio = regionSize / 200f  // 200像素作为基准（适应实际区域大小）
        val scaledSize = BASE_TEXT_SIZE * (0.7f + ratio * 1.0f)  // 0.7x到1.7x的范围
        
        return scaledSize.coerceIn(12f, 48f)  // 扩大上限，让大区域数字更大
    }

    /**
     * 极简颜色映射
     */
    private fun createSimpleColorMap(regions: List<Region>): Map<String, Int> {
        val colors = regions.map { normalizeColor(it.colorHex) }.distinct().sorted()
        return colors.mapIndexed { index, color -> color to (index + 1) }.toMap()
    }

    /**
     * 颜色标准化
     */
    private fun normalizeColor(colorHex: String): String {
        var color = colorHex.trim().lowercase()
        if (!color.startsWith("#")) color = "#$color"
        if (color.length == 4) {
            val r = color[1]
            val g = color[2]
            val b = color[3]
            color = "#$r$r$g$g$b$b"
        }
        return color
    }
}