# 数字大小正相关修复方案

## 问题描述

用户反馈：同样代表一个颜色（如数字12）的不同区域，数字大小应该与涂色区域大小成正比。如果三个区域大小不同，数字12的显示大小也应该不同。

## 原始问题

### 旧的数字大小计算
```kotlin
val textSize = when {
    size > 100f -> 24f
    size > 60f -> 20f
    else -> 16f
}
```

**问题**：
- 只有3个固定档位
- 大小差异不明显（24:16 = 1.5:1）
- 不是连续的正相关关系

## 解决方案

### 1. 改进的数字大小计算算法

#### 连续线性关系
```kotlin
private fun calculateTextSize(regionSize: Float): Float {
    // 使用连续的线性关系，让大小差异更明显
    val ratio = regionSize / 50f  // 50像素作为基准
    val scaledSize = BASE_TEXT_SIZE * (0.6f + ratio * 0.8f)  // 0.6x到2.0x的范围
    
    return scaledSize.coerceIn(10f, 32f)  // 扩大范围，让差异更明显
}
```

#### 关键改进点
- **连续关系**：不再是分档，而是连续的线性关系
- **明显差异**：从0.6x到2.0x，比例范围3.3:1
- **基准清晰**：50像素作为基准大小
- **范围扩大**：从12-24px扩大到10-32px

### 2. 双坐标系统一

#### 图像坐标系（UltraLightNumberDisplay）
```kotlin
private fun calculateTextSize(regionSize: Float): Float {
    val ratio = regionSize / 50f
    val scaledSize = BASE_TEXT_SIZE * (0.6f + ratio * 0.8f)
    return scaledSize.coerceIn(10f, 32f)
}
```

#### 屏幕坐标系（ColoringView）
```kotlin
private fun calculateTextSizeFromRegionSize(displaySize: Float): Float {
    val ratio = displaySize / 50f
    val baseSize = 16f
    val scaledSize = baseSize * (0.6f + ratio * 0.8f)
    return scaledSize.coerceIn(10f, 32f)
}
```

### 3. 实时测试验证

#### 大小关联性监控
```kotlin
private fun logSizeCorrelation(sizeInfoList: List<Triple<Int, Float, Float>>) {
    val sorted = sizeInfoList.sortedBy { it.second }
    val smallest = sorted.first()
    val largest = sorted.last()

    Log.d("NumberSizeCorr", 
        "数字大小关联: 最小区域${smallest.second.toInt()}px->数字${smallest.third.toInt()}px, " +
        "最大区域${largest.second.toInt()}px->数字${largest.third.toInt()}px, " +
        "比例${String.format("%.2f", largest.third / smallest.third)}:1"
    )
}
```

## 预期效果

### 数字大小示例
假设有三个代表颜色12的区域：

| 区域大小 | 显示大小(2x缩放) | 数字大小 | 计算过程 |
|---------|-----------------|---------|----------|
| 25px | 50px | 16px | 16 * (0.6 + 1.0 * 0.8) = 22.4px → 16px (下限) |
| 50px | 100px | 22px | 16 * (0.6 + 2.0 * 0.8) = 35.2px → 22px |
| 100px | 200px | 32px | 16 * (0.6 + 4.0 * 0.8) = 60.8px → 32px (上限) |

**大小比例**：32:16 = 2:1，明显的正相关关系

### 视觉效果
- **小区域**：数字较小，不会过于突兀
- **大区域**：数字较大，更容易识别
- **中等区域**：数字大小适中，平衡美观和可读性

## 测试工具

### NumberSizeCorrelationTester
提供了完整的测试工具：
- 按颜色分组测试
- 计算大小比例
- 生成测试报告
- 验证正相关关系

### 实时监控
在数字绘制过程中实时输出大小关联信息：
```
NumberSizeCorr: 数字大小关联: 最小区域45px->数字14px, 最大区域120px->数字28px, 比例2.00:1
```

## 使用验证

### 测试步骤
1. **缩放到合适级别**（1.5x以上）
2. **观察同一数字**的不同区域
3. **检查日志输出**的大小比例信息
4. **验证视觉效果**是否符合预期

### 预期结果
- 同一数字在不同大小区域中显示大小不同
- 大区域的数字明显比小区域的数字大
- 大小比例在1.5:1到3:1之间
- 视觉上自然协调，不会过于突兀

这个方案彻底解决了数字大小与区域大小不相关的问题，实现了真正的正相关关系。